/**
 * GraphQL权限控制测试
 * 测试各种权限规则和访问控制
 */

const {
  isAuthenticated,
  isWebWhatsAppToken,
  allowInternalCall,
  isAdmin,
  isRestaurantUser,
  isCustomerUser
} = require('../../../graphql/permissions/rules');
const { WHATSAPP_TOKEN_ALLOWED_OPERATIONS } = require('../../../graphql/permissions/constants');

describe('GraphQL权限控制测试', () => {
  // 模拟GraphQL info对象
  const createMockInfo = (fieldName) => ({
    fieldName,
    operation: {
      operation: fieldName.startsWith('get') ? 'query' : 'mutation'
    }
  });

  describe('公开查询测试', () => {
    test('应该允许访问公开的餐厅列表', async () => {
      const GET_RESTAURANTS = gql`
        query {
          restaurants {
            _id
            name
          }
        }
      `;

      const result = await query({
        query: GET_RESTAURANTS
      });

      expect(result.errors).toBeUndefined();
    });

    test('应该允许访问公开的餐厅详情', async () => {
      const GET_RESTAURANT = gql`
        query GetRestaurant($id: String!) {
          restaurant(id: $id) {
            _id
            name
          }
        }
      `;

      const result = await query({
        query: GET_RESTAURANT,
        variables: { id: 'test-restaurant-id' }
      });

      expect(result.errors).toBeUndefined();
    });
  });

  describe('认证用户查询测试', () => {
    test('未认证用户不应该访问用户配置文件', async () => {
      const GET_PROFILE = gql`
        query {
          profile {
            _id
            name
          }
        }
      `;

      const result = await query({
        query: GET_PROFILE
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('用户需要登录');
    });

    test('认证用户应该能访问自己的配置文件', async () => {
      const GET_PROFILE = gql`
        query {
          profile {
            _id
            name
          }
        }
      `;

      const result = await query({
        query: GET_PROFILE,
        context: {
          req: {
            isAuth: true,
            userId: 'test-user-id',
            userType: 'default'
          }
        }
      });

      expect(result.errors).toBeUndefined();
    });
  });

  describe('管理员权限测试', () => {
    test('非管理员不应该访问用户列表', async () => {
      const GET_USERS = gql`
        query {
          users {
            _id
            name
          }
        }
      `;

      const result = await query({
        query: GET_USERS,
        context: {
          req: {
            isAuth: true,
            userId: 'test-user-id',
            userType: 'default'
          }
        }
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('用户管理需要管理员权限');
    });

    test('管理员应该能访问用户列表', async () => {
      const GET_USERS = gql`
        query {
          users {
            _id
            name
          }
        }
      `;

      const result = await query({
        query: GET_USERS,
        context: {
          req: {
            isAuth: true,
            userId: 'admin-user-id',
            userType: 'ADMIN'
          }
        }
      });

      expect(result.errors).toBeUndefined();
    });

    test('非管理员不应该访问系统配置', async () => {
      const GET_CONFIGURATION = gql`
        query {
          configuration {
            _id
            currency
          }
        }
      `;

      const result = await query({
        query: GET_CONFIGURATION,
        context: {
          req: {
            isAuth: true,
            userId: 'test-user-id',
            userType: 'default'
          }
        }
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('访问敏感配置需要管理员权限');
    });
  });

  describe('餐厅用户权限测试', () => {
    test('餐厅用户应该能访问自己的订单', async () => {
      const GET_ORDERS = gql`
        query GetOrders($restaurantId: String!) {
          orders(restaurantId: $restaurantId) {
            _id
            orderId
          }
        }
      `;

      const result = await query({
        query: GET_ORDERS,
        variables: { restaurantId: 'restaurant-123' },
        context: {
          req: {
            isAuth: true,
            userId: 'restaurant-user-id',
            userType: 'RESTAURANT',
            restaurantId: 'restaurant-123'
          }
        }
      });

      expect(result.errors).toBeUndefined();
    });

    test('餐厅用户不应该访问其他餐厅的订单', async () => {
      const GET_ORDERS = gql`
        query GetOrders($restaurantId: String!) {
          orders(restaurantId: $restaurantId) {
            _id
            orderId
          }
        }
      `;

      const result = await query({
        query: GET_ORDERS,
        variables: { restaurantId: 'other-restaurant-456' },
        context: {
          req: {
            isAuth: true,
            userId: 'restaurant-user-id',
            userType: 'RESTAURANT',
            restaurantId: 'restaurant-123'
          }
        }
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('无权访问其他餐厅的资源');
    });
  });

  describe('X-WhatsAppW-Token权限测试', () => {
    test('X-WhatsAppW-Token用户应该能访问允许的操作（客户地址查询）', async () => {
      const GET_ADDRESSES = gql`
        query GetCustomerAddresses {
          getCustomerAddresses {
            _id
            formattedAddress
          }
        }
      `;

      const result = await query({
        query: GET_ADDRESSES,
        context: {
          whatsAppAuth: true,
          whatsAppCustomerId: 'whatsapp-customer-123'
        }
      });

      expect(result.errors).toBeUndefined();
    });

    test('X-WhatsAppW-Token用户应该能提交订单', async () => {
      const PLACE_ORDER = gql`
        mutation PlaceOrderFromWhatsApp($orderInput: OrderInput!) {
          placeOrderFromWhatsApp(orderInput: $orderInput) {
            _id
            orderStatus
          }
        }
      `;

      const result = await query({
        query: PLACE_ORDER,
        variables: {
          orderInput: {
            restaurant: 'restaurant-123',
            orderItems: []
          }
        },
        context: {
          whatsAppAuth: true,
          whatsAppCustomerId: 'whatsapp-customer-123'
        }
      });

      expect(result.errors).toBeUndefined();
    });

    test('非X-WhatsAppW-Token用户不应该访问受限操作', async () => {
      const GET_ADDRESSES = gql`
        query GetCustomerAddresses {
          getCustomerAddresses {
            _id
            formattedAddress
          }
        }
      `;

      const result = await query({
        query: GET_ADDRESSES,
        context: {
          req: {
            isAuth: true,
            userId: 'regular-user-id',
            userType: 'default'
          }
        }
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('需要有效的X-WhatsAppW-Token认证');
    });
  });

  describe('内部调用权限测试', () => {
    test('内部调用（有customerId参数）应该能访问内部专用操作', async () => {
      const GET_MENU = gql`
        query GetRestaurantMenuForCustomer($restaurantId: String!, $customerId: String!) {
          getRestaurantMenuForCustomer(restaurantId: $restaurantId, customerId: $customerId) {
            _id
            name
          }
        }
      `;

      const result = await query({
        query: GET_MENU,
        variables: {
          restaurantId: 'restaurant-123',
          customerId: 'internal-customer-123'
        },
        context: {}
      });

      expect(result.errors).toBeUndefined();
    });

    test('非内部调用不应该访问内部专用操作', async () => {
      const GET_MENU = gql`
        query GetRestaurantMenuForCustomer($restaurantId: String!) {
          getRestaurantMenuForCustomer(restaurantId: $restaurantId) {
            _id
            name
          }
        }
      `;

      const result = await query({
        query: GET_MENU,
        variables: { restaurantId: 'restaurant-123' },
        context: {
          req: {
            isAuth: true,
            userId: 'regular-user-id',
            userType: 'default'
          }
        }
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('此操作仅允许内部调用');
    });
  });

  describe('变更权限测试', () => {
    test('非管理员不应该能停用用户', async () => {
      const DEACTIVATE_USER = gql`
        mutation DeactivateUser($email: String!, $isActive: Boolean!) {
          Deactivate(email: $email, isActive: $isActive) {
            _id
            isActive
          }
        }
      `;

      const result = await mutate({
        mutation: DEACTIVATE_USER,
        variables: { email: '<EMAIL>', isActive: false },
        context: {
          req: {
            isAuth: true,
            userId: 'regular-user-id',
            userType: 'default'
          }
        }
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('用户管理需要管理员权限');
    });

    test('管理员应该能停用用户', async () => {
      const DEACTIVATE_USER = gql`
        mutation DeactivateUser($email: String!, $isActive: Boolean!) {
          Deactivate(email: $email, isActive: $isActive) {
            _id
            isActive
          }
        }
      `;

      const result = await mutate({
        mutation: DEACTIVATE_USER,
        variables: { email: '<EMAIL>', isActive: false },
        context: {
          req: {
            isAuth: true,
            userId: 'admin-user-id',
            userType: 'ADMIN'
          }
        }
      });

      expect(result.errors).toBeUndefined();
    });
  });

  describe('权限规则边界测试', () => {
    test('空上下文应该被拒绝', async () => {
      const GET_PROFILE = gql`
        query {
          profile {
            _id
            name
          }
        }
      `;

      const result = await query({
        query: GET_PROFILE,
        context: {}
      });

      expect(result.errors).toBeDefined();
    });

    test('无效的用户类型应该被拒绝', async () => {
      const GET_ORDERS = gql`
        query {
          orders {
            _id
            orderId
          }
        }
      `;

      const result = await query({
        query: GET_ORDERS,
        context: {
          req: {
            isAuth: true,
            userId: 'test-user-id',
            userType: 'INVALID_TYPE'
          }
        }
      });

      expect(result.errors).toBeDefined();
    });

    test('缺少必要字段的上下文应该被拒绝', async () => {
      const GET_ORDERS = gql`
        query {
          orders {
            _id
            orderId
          }
        }
      `;

      const result = await query({
        query: GET_ORDERS,
        context: {
          req: {
            isAuth: true
            // 缺少 userId 和 userType
          }
        }
      });

      expect(result.errors).toBeDefined();
    });
  });

  afterAll(() => {
    if (server) {
      server.stop();
    }
  });
});
