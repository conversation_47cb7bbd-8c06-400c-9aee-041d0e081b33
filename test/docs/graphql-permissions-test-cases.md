# GraphQL权限控制测试用例文档

## 概述

本文档详细描述了GraphQL权限控制系统的测试用例设计，基于重新设计的权限架构，确保系统安全性和功能正确性。

## 🎯 测试目标

### 主要测试目标
1. **验证权限规则正确性** - 确保各种权限规则按预期工作
2. **验证认证机制安全性** - 确保不同认证方式的安全隔离
3. **验证访问控制有效性** - 确保未授权访问被正确拒绝
4. **验证环境配置安全** - 确保生产环境的安全配置

### 测试覆盖范围
- 基础认证规则测试
- X-WhatsAppW-Token受限访问测试
- 内部调用权限测试
- 角色权限测试
- 环境配置测试
- 安全攻击防护测试

## 📋 测试用例设计

### 1. 基础认证规则测试

#### 1.1 JWT认证测试
```yaml
测试用例: JWT-AUTH-001
描述: 验证有效JWT token的认证
前置条件: 
  - 用户已注册并获得有效JWT token
测试步骤:
  1. 使用Bearer token发送GraphQL请求
  2. 验证isAuthenticated规则返回true
预期结果: 认证成功，允许访问

测试用例: JWT-AUTH-002
描述: 验证无效JWT token的拒绝
前置条件:
  - 使用过期或无效的JWT token
测试步骤:
  1. 使用无效token发送GraphQL请求
  2. 验证isAuthenticated规则返回Error
预期结果: 认证失败，拒绝访问
```

### 2. X-WhatsAppW-Token权限测试

#### 2.1 允许的操作测试
```yaml
测试用例: WHATSAPP-WEB-001
描述: 验证X-WhatsAppW-Token访问允许的查询操作
前置条件:
  - 有效的X-WhatsAppW-Token
  - whatsAppAuth: true, whatsAppCustomerId: 存在
测试步骤:
  1. 使用X-WhatsAppW-Token请求getCustomerAddresses
  2. 验证isWebWhatsAppToken规则返回true
预期结果: 访问成功

测试用例: WHATSAPP-WEB-002
描述: 验证X-WhatsAppW-Token访问允许的变更操作
前置条件:
  - 有效的X-WhatsAppW-Token
测试步骤:
  1. 使用X-WhatsAppW-Token请求placeOrderFromWhatsApp
  2. 验证isWebWhatsAppToken规则返回true
预期结果: 访问成功
```

#### 2.2 拒绝的操作测试
```yaml
测试用例: WHATSAPP-WEB-003
描述: 验证X-WhatsAppW-Token访问未授权操作被拒绝
前置条件:
  - 有效的X-WhatsAppW-Token
测试步骤:
  1. 使用X-WhatsAppW-Token请求getRestaurantMenuForCustomer
  2. 验证isWebWhatsAppToken规则返回Error
预期结果: 访问被拒绝，错误信息包含"不允许访问操作"

测试用例: WHATSAPP-WEB-004
描述: 验证无效X-WhatsAppW-Token被拒绝
前置条件:
  - whatsAppAuth: false 或 whatsAppCustomerId: null
测试步骤:
  1. 使用无效token请求getCustomerAddresses
  2. 验证isWebWhatsAppToken规则返回Error
预期结果: 访问被拒绝，错误信息包含"需要有效的X-WhatsAppW-Token认证"
```

### 3. 内部调用权限测试

#### 3.1 内部调用成功测试
```yaml
测试用例: INTERNAL-CALL-001
描述: 验证带customerId参数的内部调用
前置条件:
  - 请求参数包含customerId
测试步骤:
  1. 调用getRestaurantMenuForCustomer，传入customerId参数
  2. 验证allowInternalCall规则返回true
预期结果: 内部调用成功

测试用例: INTERNAL-CALL-002
描述: 验证内部调用绕过HTTP权限检查
前置条件:
  - 服务器内部直接调用GraphQL resolver
测试步骤:
  1. 直接调用resolver函数，传入customerId
  2. 验证不触发HTTP权限验证
预期结果: 调用成功，无权限限制
```

#### 3.2 内部调用失败测试
```yaml
测试用例: INTERNAL-CALL-003
描述: 验证缺少customerId参数的调用被拒绝
前置条件:
  - 请求参数不包含customerId
测试步骤:
  1. 调用getRestaurantMenuForCustomer，不传入customerId
  2. 验证allowInternalCall规则返回Error
预期结果: 调用被拒绝，错误信息包含"仅允许内部调用"
```

### 4. 角色权限测试

#### 4.1 管理员权限测试
```yaml
测试用例: ROLE-ADMIN-001
描述: 验证管理员用户访问管理功能
前置条件:
  - 用户类型为ADMIN
  - 有效的JWT认证
测试步骤:
  1. 请求管理员专用操作
  2. 验证isAdmin规则返回true
预期结果: 访问成功

测试用例: ROLE-ADMIN-002
描述: 验证非管理员用户被拒绝访问管理功能
前置条件:
  - 用户类型为default或其他非ADMIN类型
测试步骤:
  1. 请求管理员专用操作
  2. 验证isAdmin规则返回Error
预期结果: 访问被拒绝，错误信息包含"需要管理员权限"
```

#### 4.2 餐厅用户权限测试
```yaml
测试用例: ROLE-RESTAURANT-001
描述: 验证餐厅用户访问餐厅功能
前置条件:
  - 用户类型为RESTAURANT
测试步骤:
  1. 请求餐厅专用操作
  2. 验证isRestaurantUser规则返回true
预期结果: 访问成功

测试用例: ROLE-RESTAURANT-002
描述: 验证非餐厅用户被拒绝访问餐厅功能
前置条件:
  - 用户类型为default或其他非RESTAURANT类型
测试步骤:
  1. 请求餐厅专用操作
  2. 验证isRestaurantUser规则返回Error
预期结果: 访问被拒绝，错误信息包含"需要餐厅用户权限"
```

### 5. 环境配置测试

#### 5.1 生产环境安全配置测试
```yaml
测试用例: ENV-SECURITY-001
描述: 验证生产环境禁用introspection
前置条件:
  - NODE_ENV设置为production
测试步骤:
  1. 发送introspection查询
  2. 验证返回错误或空结果
预期结果: introspection被禁用

测试用例: ENV-SECURITY-002
描述: 验证生产环境禁用playground
前置条件:
  - NODE_ENV设置为production
测试步骤:
  1. 访问GraphQL playground端点
  2. 验证返回404或禁用页面
预期结果: playground被禁用
```

#### 5.2 本地环境开发配置测试
```yaml
测试用例: ENV-DEV-001
描述: 验证本地环境启用introspection
前置条件:
  - NODE_ENV设置为local
测试步骤:
  1. 发送introspection查询
  2. 验证返回完整schema信息
预期结果: introspection正常工作

测试用例: ENV-DEV-002
描述: 验证本地环境启用playground
前置条件:
  - NODE_ENV设置为local
测试步骤:
  1. 访问GraphQL playground端点
  2. 验证返回playground界面
预期结果: playground正常工作
```

## 🔒 安全测试用例

### 6. 权限提升攻击测试

#### 6.1 跨权限访问测试
```yaml
测试用例: SECURITY-001
描述: 验证X-WhatsAppW-Token无法访问管理员功能
前置条件:
  - 使用X-WhatsAppW-Token认证
测试步骤:
  1. 尝试访问管理员专用操作
  2. 验证访问被拒绝
预期结果: 权限提升攻击被阻止

测试用例: SECURITY-002
描述: 验证普通用户无法访问其他用户资源
前置条件:
  - 普通用户JWT token
测试步骤:
  1. 尝试访问其他用户的订单或数据
  2. 验证资源所有权检查生效
预期结果: 跨用户访问被阻止
```

### 7. 边界条件测试

#### 7.1 空值和异常输入测试
```yaml
测试用例: BOUNDARY-001
描述: 验证空token的处理
前置条件:
  - 不提供任何认证token
测试步骤:
  1. 发送需要认证的GraphQL请求
  2. 验证返回适当的认证错误
预期结果: 认证失败，返回明确错误信息

测试用例: BOUNDARY-002
描述: 验证格式错误token的处理
前置条件:
  - 提供格式错误的token
测试步骤:
  1. 发送带有错误格式token的请求
  2. 验证返回token格式错误
预期结果: 认证失败，返回格式错误信息
```

## 📊 测试执行指南

### 测试环境准备
1. **本地测试环境** - 用于开发和调试
2. **集成测试环境** - 用于完整功能测试
3. **安全测试环境** - 用于安全漏洞测试

### 测试数据准备
1. **有效用户账户** - 不同角色的测试用户
2. **有效token** - JWT和X-WhatsAppW-Token
3. **测试订单数据** - 用于权限验证测试

### 测试执行步骤
1. **单元测试** - 测试单个权限规则
2. **集成测试** - 测试完整权限流程
3. **端到端测试** - 测试真实场景
4. **安全测试** - 测试攻击防护

### 测试结果验证
1. **功能正确性** - 权限规则按预期工作
2. **安全有效性** - 未授权访问被正确阻止
3. **性能影响** - 权限检查不影响系统性能
4. **错误处理** - 错误信息清晰且安全

## 📝 测试报告模板

### 测试执行报告
```
测试日期: [日期]
测试环境: [环境信息]
测试版本: [代码版本]

测试结果统计:
- 总测试用例数: [数量]
- 通过用例数: [数量]
- 失败用例数: [数量]
- 跳过用例数: [数量]

关键发现:
- [重要发现1]
- [重要发现2]

建议改进:
- [改进建议1]
- [改进建议2]
```

## 🔄 持续测试策略

### 自动化测试
- 集成到CI/CD流程
- 每次代码提交自动运行
- 定期安全扫描

### 回归测试
- 权限规则变更后的完整测试
- 新功能添加后的权限验证
- 安全补丁后的验证测试

### 监控和告警
- 生产环境权限异常监控
- 异常访问尝试告警
- 权限规则性能监控
