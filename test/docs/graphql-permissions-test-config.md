# GraphQL权限控制测试配置文档

## 概述

本文档描述了GraphQL权限控制系统测试的配置方法、测试框架设置和最佳实践。

## 🛠️ 测试框架配置

### Jest配置
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/test/setup.js'],
  testMatch: [
    '<rootDir>/test/unit/graphql/**/*.test.js',
    '<rootDir>/test/integration/graphql/**/*.test.js'
  ],
  collectCoverageFrom: [
    'graphql/permissions/**/*.js',
    '!graphql/permissions/index.js'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

### 测试环境设置
```javascript
// test/setup.js
// Mock logger before any imports
jest.mock('../helpers/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

// Mock configuration
jest.mock('../config', () => ({
  NODE_ENV: 'test',
  JWT_SECRET: 'test-secret',
  WHATSAPP_WEBHOOK_SECRET: 'test-webhook-secret'
}));

// Global test utilities
global.createMockContext = (overrides = {}) => ({
  req: { isAuth: false },
  whatsAppAuth: false,
  whatsAppCustomerId: null,
  ...overrides
});

global.createMockInfo = (fieldName) => ({
  fieldName,
  operation: {
    operation: fieldName.startsWith('get') ? 'query' : 'mutation'
  }
});
```

## 📋 测试数据配置

### 测试用户数据
```javascript
// test/fixtures/users.js
module.exports = {
  adminUser: {
    id: 'admin-user-123',
    userType: 'ADMIN',
    email: '<EMAIL>'
  },
  restaurantUser: {
    id: 'restaurant-user-123',
    userType: 'RESTAURANT',
    email: '<EMAIL>'
  },
  customerUser: {
    id: 'customer-user-123',
    userType: 'default',
    email: '<EMAIL>'
  },
  whatsappCustomer: {
    id: 'whatsapp-customer-123',
    phone: '+1234567890'
  }
};
```

### 测试Token配置
```javascript
// test/fixtures/tokens.js
const jwt = require('jsonwebtoken');
const config = require('../../config');

module.exports = {
  validJWT: jwt.sign(
    { userId: 'user-123', userType: 'default' },
    config.JWT_SECRET,
    { expiresIn: '1h' }
  ),
  expiredJWT: jwt.sign(
    { userId: 'user-123', userType: 'default' },
    config.JWT_SECRET,
    { expiresIn: '-1h' }
  ),
  adminJWT: jwt.sign(
    { userId: 'admin-123', userType: 'ADMIN' },
    config.JWT_SECRET,
    { expiresIn: '1h' }
  ),
  validWhatsAppToken: 'valid-whatsapp-token-123',
  invalidWhatsAppToken: 'invalid-token'
};
```

## 🧪 单元测试配置

### 权限规则单元测试
```javascript
// test/unit/graphql/permissions/rules.test.js
const {
  isAuthenticated,
  isWebWhatsAppToken,
  allowInternalCall,
  isAdmin,
  isRestaurantUser,
  isCustomerUser
} = require('../../../../graphql/permissions/rules');

describe('GraphQL权限规则单元测试', () => {
  describe('isAuthenticated', () => {
    test('应该允许已认证用户', async () => {
      const context = createMockContext({
        req: { isAuth: true }
      });
      
      const result = await isAuthenticated.resolve(
        null, {}, context, createMockInfo('testQuery')
      );
      
      expect(result).toBe(true);
    });

    test('应该拒绝未认证用户', async () => {
      const context = createMockContext({
        req: { isAuth: false }
      });
      
      const result = await isAuthenticated.resolve(
        null, {}, context, createMockInfo('testQuery')
      );
      
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toContain('用户需要登录');
    });
  });

  describe('isWebWhatsAppToken', () => {
    test('应该允许访问允许的操作', async () => {
      const context = createMockContext({
        whatsAppAuth: true,
        whatsAppCustomerId: 'customer-123'
      });
      
      const result = await isWebWhatsAppToken.resolve(
        null, {}, context, createMockInfo('getCustomerAddresses')
      );
      
      expect(result).toBe(true);
    });

    test('应该拒绝访问未授权操作', async () => {
      const context = createMockContext({
        whatsAppAuth: true,
        whatsAppCustomerId: 'customer-123'
      });
      
      const result = await isWebWhatsAppToken.resolve(
        null, {}, context, createMockInfo('unauthorizedOperation')
      );
      
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toContain('不允许访问操作');
    });
  });

  describe('allowInternalCall', () => {
    test('应该允许有customerId的内部调用', async () => {
      const args = { customerId: 'customer-123' };
      
      const result = await allowInternalCall.resolve(
        null, args, {}, createMockInfo('internalOperation')
      );
      
      expect(result).toBe(true);
    });

    test('应该拒绝没有customerId的调用', async () => {
      const args = {};
      
      const result = await allowInternalCall.resolve(
        null, args, {}, createMockInfo('internalOperation')
      );
      
      expect(result).toBeInstanceOf(Error);
      expect(result.message).toContain('仅允许内部调用');
    });
  });
});
```

## 🔗 集成测试配置

### GraphQL服务器测试设置
```javascript
// test/integration/graphql/permissions.test.js
const { ApolloServer } = require('apollo-server-express');
const { createTestClient } = require('apollo-server-testing');
const typeDefs = require('../../../graphql/typeDefs');
const resolvers = require('../../../graphql/resolvers');
const permissions = require('../../../graphql/permissions');

describe('GraphQL权限集成测试', () => {
  let server, query, mutate;

  beforeAll(() => {
    server = new ApolloServer({
      typeDefs,
      resolvers,
      middlewares: [permissions],
      context: ({ req }) => ({
        req,
        // Mock context based on test needs
      })
    });

    const testClient = createTestClient(server);
    query = testClient.query;
    mutate = testClient.mutate;
  });

  describe('X-WhatsAppW-Token权限集成测试', () => {
    test('应该允许访问getCustomerAddresses', async () => {
      const GET_CUSTOMER_ADDRESSES = gql`
        query GetCustomerAddresses {
          getCustomerAddresses {
            id
            address
          }
        }
      `;

      const result = await query({
        query: GET_CUSTOMER_ADDRESSES,
        http: {
          headers: {
            'X-WhatsAppW-Token': 'valid-token'
          }
        }
      });

      expect(result.errors).toBeUndefined();
      expect(result.data.getCustomerAddresses).toBeDefined();
    });

    test('应该拒绝访问未授权操作', async () => {
      const UNAUTHORIZED_QUERY = gql`
        query GetRestaurantMenu {
          getRestaurantMenuForCustomer {
            id
            name
          }
        }
      `;

      const result = await query({
        query: UNAUTHORIZED_QUERY,
        http: {
          headers: {
            'X-WhatsAppW-Token': 'valid-token'
          }
        }
      });

      expect(result.errors).toBeDefined();
      expect(result.errors[0].message).toContain('不允许访问操作');
    });
  });
});
```

## 🔒 安全测试配置

### 权限提升攻击测试
```javascript
// test/security/graphql/permission-escalation.test.js
describe('GraphQL权限提升攻击测试', () => {
  test('X-WhatsAppW-Token无法访问管理员功能', async () => {
    const ADMIN_QUERY = gql`
      query GetAllUsers {
        allUsers {
          id
          email
        }
      }
    `;

    const result = await query({
      query: ADMIN_QUERY,
      http: {
        headers: {
          'X-WhatsAppW-Token': 'valid-whatsapp-token'
        }
      }
    });

    expect(result.errors).toBeDefined();
    expect(result.errors[0].message).toContain('需要管理员权限');
  });

  test('普通用户无法访问其他用户资源', async () => {
    const OTHER_USER_QUERY = gql`
      query GetUserOrders($userId: ID!) {
        orders(userId: $userId) {
          id
          status
        }
      }
    `;

    const result = await query({
      query: OTHER_USER_QUERY,
      variables: { userId: 'other-user-id' },
      http: {
        headers: {
          'Authorization': 'Bearer ' + tokens.customerJWT
        }
      }
    });

    expect(result.errors).toBeDefined();
    expect(result.errors[0].message).toContain('权限不足');
  });
});
```

## 📊 性能测试配置

### 权限检查性能测试
```javascript
// test/performance/graphql/permissions.test.js
describe('GraphQL权限性能测试', () => {
  test('权限检查不应显著影响响应时间', async () => {
    const startTime = Date.now();
    
    const result = await query({
      query: SIMPLE_QUERY,
      http: {
        headers: {
          'Authorization': 'Bearer ' + tokens.validJWT
        }
      }
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    expect(result.errors).toBeUndefined();
    expect(responseTime).toBeLessThan(100); // 100ms threshold
  });

  test('大量并发权限检查', async () => {
    const promises = Array(100).fill().map(() => 
      query({
        query: SIMPLE_QUERY,
        http: {
          headers: {
            'Authorization': 'Bearer ' + tokens.validJWT
          }
        }
      })
    );

    const startTime = Date.now();
    const results = await Promise.all(promises);
    const endTime = Date.now();

    const avgResponseTime = (endTime - startTime) / 100;
    
    results.forEach(result => {
      expect(result.errors).toBeUndefined();
    });
    
    expect(avgResponseTime).toBeLessThan(50); // 50ms average
  });
});
```

## 🎯 测试覆盖率配置

### 覆盖率报告设置
```javascript
// jest.config.js - 覆盖率配置
module.exports = {
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'graphql/permissions/**/*.js',
    '!**/node_modules/**',
    '!**/test/**'
  ],
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    },
    './graphql/permissions/rules.js': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
};
```

## 🔄 CI/CD集成配置

### GitHub Actions配置
```yaml
# .github/workflows/graphql-permissions-test.yml
name: GraphQL权限测试

on:
  push:
    paths:
      - 'graphql/permissions/**'
      - 'test/**'
  pull_request:
    paths:
      - 'graphql/permissions/**'
      - 'test/**'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run unit tests
      run: npm run test:unit:permissions
      
    - name: Run integration tests
      run: npm run test:integration:permissions
      
    - name: Run security tests
      run: npm run test:security:permissions
      
    - name: Generate coverage report
      run: npm run test:coverage:permissions
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
```

## 📝 测试脚本配置

### package.json测试脚本
```json
{
  "scripts": {
    "test:permissions": "jest test/unit/graphql/permissions test/integration/graphql/permissions",
    "test:unit:permissions": "jest test/unit/graphql/permissions",
    "test:integration:permissions": "jest test/integration/graphql/permissions",
    "test:security:permissions": "jest test/security/graphql/permissions",
    "test:coverage:permissions": "jest --coverage test/unit/graphql/permissions test/integration/graphql/permissions",
    "test:watch:permissions": "jest --watch test/unit/graphql/permissions"
  }
}
```

## 🛡️ 安全测试最佳实践

### 1. 测试数据安全
- 使用模拟数据，避免真实敏感信息
- 定期轮换测试token和密钥
- 测试环境与生产环境完全隔离

### 2. 权限测试完整性
- 测试所有权限规则的正面和负面场景
- 验证权限组合的正确性
- 测试边界条件和异常情况

### 3. 持续安全验证
- 每次代码变更后运行完整权限测试
- 定期进行安全渗透测试
- 监控生产环境的权限异常

### 4. 测试文档维护
- 保持测试用例与权限规则同步
- 记录测试覆盖的安全场景
- 定期审查和更新测试策略
