const { shield, and, deny, allow } = require('graphql-shield');
const {
  isAuthenticated,
  isWebWhatsAppToken,
  allowInternalCall,
  isAdmin,
  isRestaurantUser,
  isCustomerUser,
  isResourceOwner,
  isOrderOwner,
  isSensitiveConfigurationAccess,
  isUserManager,
  isSelfManagement
} = require('./permissions/rules');



const permissions = shield(
  {
    Query: {
      // === 公开查询（无需认证）===
      restaurants: allow,
      restaurant: allow,
      restaurantPreview: allow,
      restaurantsPreview: allow,
      restaurantList: allow,
      restaurantListPreview: allow,
      categories: allow,
      foods: allow,
      foodByCategory: allow,
      foodByIds: allow,
      banners: allow,
      bannerActions: allow,
      cuisines: allow,
      addons: allow,
      options: allow,
      taxes: allow,
      tips: allow,
      zones: allow,
      zone: allow,
      coupons: allow,
      offers: allow,
      sections: allow,
      getCountries: allow,
      getCountryByIso: allow,
      popularItems: allow,
      relatedItems: allow,

      // === X-WhatsAppW-Token专用查询（受限访问）===
      customerAddresses: isWebWhatsAppToken,
      getAddressFromPostcode: isWebWhatsAppToken,
      getSessionByToken: isWebWhatsAppToken,

      // === 内部调用专用查询（系统内部使用）===
      customerbyPhone: allowInternalCall,
      customerbyPhoneAll: allowInternalCall,

      // === 客户查询 ===
      profile: and(isAuthenticated, isCustomerUser),
      userFavourite: and(isAuthenticated, isCustomerUser),

      // === 餐厅查询 ===
      orders: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      order: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      orderDetails: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      restaurantOrders: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      ordersByRestId: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      orderCount: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      pageCount: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      getRefund: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      getOrderRefunds: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      getDashboardTotal: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      getDashboardOrders: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      getDashboardSales: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      getOrdersByDateRange: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      getActiveOrders: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      restaurantByOwner: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      reviews: and(isAuthenticated, isRestaurantUser, isResourceOwner),

      // === 品牌查询 ===
      brand: allow,
      brands: allow,

      // === 管理员专用查询 ===
      users: isUserManager,
      configuration: isSensitiveConfigurationAccess,
      allOrders: isAdmin,
      assignedOrders: isAdmin,
      deliveredOrders: isAdmin,
      undeliveredOrders: isAdmin,
      getOrderStatuses: isAdmin,
      getPaymentStatuses: isAdmin,
      vendors: isAdmin,
      getVendor: isAdmin,
      earnings: isAdmin,
      withdrawRequests: isAdmin,
      getAllWithdrawRequests: isAdmin,

      // === 骑手管理查询（管理员专用）===
      rider: isAdmin,
      riders: isAdmin,
      ridersByZone: isAdmin,
      riderCompletedOrders: isAdmin,
      riderEarnings: isAdmin,
      riderOrders: isAdmin,
      riderWithdrawRequests: isAdmin,
      availableRiders: isAdmin,
      unassignedOrdersByZone: isAdmin,

      // === 其他需要认证的查询 ===
      likedFood: isAuthenticated,
      orderPaypal: and(isAuthenticated, isOrderOwner),
      orderStripe: and(isAuthenticated, isOrderOwner),

      // === 地理位置相关查询（公开）===
      nearByRestaurants: allow,
      nearByRestaurantsPreview: allow,
      topRatedVendors: allow,
      topRatedVendorsPreview: allow,
      recentOrderRestaurants: allow,
      recentOrderRestaurantsPreview: allow,
      mostOrderedRestaurants: allow,
      mostOrderedRestaurantsPreview: allow,

      // === 聊天查询 ===
      chat: and(isAuthenticated, isOrderOwner),

      // === 演示查询（管理员专用）===
      lastOrderCreds: isAdmin
    },

    Mutation: {
      // === 公开变更 (注册等) ===
      createUser: allow,
      login: allow,

      // === X-WhatsAppW-Token专用变更（受限访问）===
      placeOrderWhatsApp: isWebWhatsAppToken,

      // === 客户变更 ===
      updateUser: and(isAuthenticated, isSelfManagement),
      placeOrder: and(isAuthenticated, isCustomerUser),
      addCustomerAddress: and(isAuthenticated, isCustomerUser),
      deleteCustomerAddress: and(isAuthenticated, isCustomerUser),
      createAddress: and(isAuthenticated, isCustomerUser),
      editAddress: and(isAuthenticated, isCustomerUser),
      deleteAddress: and(isAuthenticated, isCustomerUser),
      deleteBulkAddresses: and(isAuthenticated, isCustomerUser),
      selectAddress: and(isAuthenticated, isCustomerUser),
      addFavourite: and(isAuthenticated, isCustomerUser),
      updateNotificationStatus: and(isAuthenticated, isCustomerUser),
      pushToken: and(isAuthenticated, isCustomerUser),
      changePassword: and(isAuthenticated, isSelfManagement),
      reviewOrder: and(isAuthenticated, isCustomerUser),

      // === 餐厅变更 ===
      updateOrderStatus: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      refundOrder: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      createRestaurant: and(isAuthenticated, isRestaurantUser),
      editRestaurant: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      deleteRestaurant: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      createFood: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      editFood: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      deleteFood: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      createCategory: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      editCategory: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      deleteCategory: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      createAddons: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      editAddon: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      deleteAddon: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      createOptions: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      editOption: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      deleteOption: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      toggleAvailability: and(isAuthenticated, isRestaurantUser),
      toggleMenuFood: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      saveRestaurantToken: and(isAuthenticated, isRestaurantUser),
      updateTimings: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      updateDeliveryBoundsAndLocation: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      restaurantLogin: allow,
      acceptOrder: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      orderPickedUp: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      abortOrder: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      cancelOrder: and(isAuthenticated, isRestaurantUser, isResourceOwner),

      // === 品牌变更 ===
      createBrand: isAdmin,
      updateBrand: isAdmin,
      deleteBrand: isAdmin,
      addRestaurantToBrand: isAdmin,
      removeRestaurantFromBrand: isAdmin,

      // === 内容管理变更（管理员专用）===
      createBanner: isAdmin,
      editBanner: isAdmin,
      deleteBanner: isAdmin,
      createCuisine: isAdmin,
      editCuisine: isAdmin,
      deleteCuisine: isAdmin,

      // === 管理员专用变更 ===
      Deactivate: isUserManager,
      adminLogin: allow,
      ownerLogin: allow,
      saveEmailConfiguration: isSensitiveConfigurationAccess,
      saveFormEmailConfiguration: isSensitiveConfigurationAccess,
      saveSendGridConfiguration: isSensitiveConfigurationAccess,
      savePaypalConfiguration: isSensitiveConfigurationAccess,
      saveStripeConfiguration: isSensitiveConfigurationAccess,
      saveTwilioConfiguration: isSensitiveConfigurationAccess,
      saveFirebaseConfiguration: isSensitiveConfigurationAccess,
      saveSentryConfiguration: isSensitiveConfigurationAccess,
      saveGoogleApiKeyConfiguration: isSensitiveConfigurationAccess,
      saveCloudinaryConfiguration: isSensitiveConfigurationAccess,
      saveAmplitudeApiKeyConfiguration: isSensitiveConfigurationAccess,
      saveGoogleClientIDConfiguration: isSensitiveConfigurationAccess,
      saveWebConfiguration: isSensitiveConfigurationAccess,
      saveAppConfigurations: isSensitiveConfigurationAccess,
      saveDemoConfiguration: isSensitiveConfigurationAccess,
      saveCurrencyConfiguration: isSensitiveConfigurationAccess,
      saveDeliveryRateConfiguration: isSensitiveConfigurationAccess,
      saveVerificationsToggle: isSensitiveConfigurationAccess,
      updateCommission: isAdmin,

      // === 系统管理变更（管理员专用）===
      createVendor: isAdmin,
      editVendor: isAdmin,
      deleteVendor: isAdmin,
      uploadToken: isAdmin,
      vendorResetPassword: isAdmin,
      createZone: isAdmin,
      editZone: isAdmin,
      deleteZone: isAdmin,
      createOffer: isAdmin,
      editOffer: isAdmin,
      deleteOffer: isAdmin,
      addRestaurantToOffer: isAdmin,
      createSection: isAdmin,
      editSection: isAdmin,
      deleteSection: isAdmin,
      createTaxation: isAdmin,
      editTaxation: isAdmin,
      createTipping: isAdmin,
      editTipping: isAdmin,
      createCoupon: isAdmin,
      editCoupon: isAdmin,
      deleteCoupon: isAdmin,
      coupon: isAdmin,
      banner: isAdmin,
      cuisine: isAdmin,

      // === 骑手管理变更（管理员专用）===
      createRider: isAdmin,
      editRider: isAdmin,
      deleteRider: isAdmin,
      riderLogin: allow,
      toggleAvailablity: isAdmin,
      assignOrder: isAdmin,
      assignRider: isAdmin,
      updateOrderStatusRider: isAdmin,
      updateRiderLocation: isAdmin,
      notifyRiders: isAdmin,

      // === 收益和提现管理（管理员专用）===
      createEarning: isAdmin,
      createWithdrawRequest: isAdmin,
      updateWithdrawReqStatus: isAdmin,

      // === 聊天变更 ===
      sendChatMessage: and(isAuthenticated, isOrderOwner),

      // === 通知和表单变更 ===
      sendNotificationUser: isAdmin,
      saveNotificationTokenWeb: isAuthenticated,
      sendFormSubmission: allow,
      sendOtpToEmail: allow,
      sendOtpToPhoneNumber: allow,
      emailExist: allow,
      phoneExist: allow,
      forgotPassword: allow,
      resetPassword: allow,
      muteRing: and(isAuthenticated, isOrderOwner),

      // === 其他需要认证的变更 ===
      editOrder: and(isAuthenticated, isOrderOwner),
      likeFood: and(isAuthenticated, isCustomerUser),
      updateStatus: and(isAuthenticated, isOrderOwner),
      updatePaymentStatus: and(isAuthenticated, isOrderOwner)
    },

    Subscription: {
      // === 订单状态订阅 ===
      SubscribeOrderStatus: and(isAuthenticated, isCustomerUser),
      subscribePlaceOrder: and(isAuthenticated, isRestaurantUser, isResourceOwner),
      subscriptionOrder: and(isAuthenticated, isOrderOwner),

      // === 骑手相关订阅（管理员专用）===
      subscriptionAssignRider: isAdmin,
      subscriptionRiderLocation: isAdmin,
      subscriptionDispatcher: isAdmin,
      subscriptionZoneOrders: isAdmin,

      // === 聊天订阅 ===
      subscriptionNewMessage: and(isAuthenticated, isOrderOwner)
    },

    // === 类型字段权限 ===
    // 对于大多数类型，如果能访问类型本身，就应该能访问其所有字段
    // 使用通配符 "*" 来简化配置
    CustomerAddress: allow,
    Coordinates: allow,
    Customer: allow,
    User: allow,
    Restaurant: allow,
    Order: allow,
    Food: allow,
    Category: allow,
    Addon: allow,
    Option: allow,
    Zone: allow,
    Banner: allow,
    Coupon: allow,
    Offer: allow,
    Section: allow,
    Country: allow,
    City: allow,
    Brand: allow,
    RestaurantBrief: allow,
    Rider: allow,
    Earnings: allow,
    WithdrawRequest: allow,
    Point: allow,
    GeoPoint: allow,
    Polygon: allow,
    Location: allow,
    Admin: allow,
    AuthData: allow,
    RestaurantAuth: allow,
    Address: allow,
    AddressDetails: allow,
    OrderBrief: allow,
    PopularItemsResponse: allow,
    DemoCredentails: allow
  },
  {
    fallbackRule: deny, // 默认拒绝所有未明确允许的访问
    allowExternalErrors: true, // 允许将规则中的Error消息返回给客户端
    debug: process.env.NODE_ENV !== 'production' // 在非生产环境开启调试信息
  }
);

module.exports = permissions;
