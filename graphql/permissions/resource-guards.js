/**
 * 资源级权限守卫
 * 实现基于资源所有权的细粒度权限控制
 */

const { rule } = require('graphql-shield');
const { USER_ROLES } = require('./constants');
const logger = require('../../helpers/logger');

// 导入模型用于资源所有权检查
const Order = require('../../models/order');
const Restaurant = require('../../models/restaurant');
const User = require('../../models/user');

/**
 * 餐厅资源守卫 - 确保餐厅用户只能访问自己的餐厅资源
 */
const restaurantResourceGuard = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { restaurantId, userType, userId } = ctx.req;
    
    // 管理员可以访问所有餐厅资源
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    // 非餐厅用户不能访问餐厅资源
    if (userType !== USER_ROLES.RESTAURANT) {
      logger.warn(`非餐厅用户尝试访问餐厅资源: ${info.fieldName}, 用户类型: ${userType}`);
      return new Error('无权访问餐厅资源');
    }
    
    // 检查各种可能的餐厅ID参数
    const requestedRestaurantId = args.restaurant || args.restaurantId || args.id;
    
    if (requestedRestaurantId && requestedRestaurantId !== restaurantId) {
      logger.warn(`餐厅用户尝试访问其他餐厅资源: ${info.fieldName}, 请求餐厅: ${requestedRestaurantId}, 用户餐厅: ${restaurantId}`);
      return new Error('只能访问自己的餐厅资源');
    }
    
    return true;
  }
);

/**
 * 订单资源守卫 - 确保用户只能访问相关的订单
 */
const orderResourceGuard = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { restaurantId, userId, userType } = ctx.req;
    
    // 管理员可以访问所有订单
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    const orderId = args.orderId || args.id;
    
    // 如果没有指定订单ID，允许通过（可能是列表查询）
    if (!orderId) {
      return true;
    }
    
    try {
      // 查询订单信息
      const order = await Order.findById(orderId);
      if (!order) {
        logger.warn(`尝试访问不存在的订单: ${orderId}`);
        return new Error('订单不存在');
      }
      
      // 餐厅用户只能访问自己餐厅的订单
      if (userType === USER_ROLES.RESTAURANT) {
        if (order.restaurant.toString() !== restaurantId) {
          logger.warn(`餐厅用户尝试访问其他餐厅订单: ${orderId}, 订单餐厅: ${order.restaurant}, 用户餐厅: ${restaurantId}`);
          return new Error('只能访问自己餐厅的订单');
        }
      }
      
      // 客户用户只能访问自己的订单
      if (userType === USER_ROLES.CUSTOMER) {
        if (order.user.toString() !== userId) {
          logger.warn(`客户用户尝试访问其他用户订单: ${orderId}, 订单用户: ${order.user}, 当前用户: ${userId}`);
          return new Error('只能访问自己的订单');
        }
      }
      
      return true;
    } catch (error) {
      logger.error(`订单资源守卫检查失败: ${error.message}`);
      return new Error('订单访问权限检查失败');
    }
  }
);

/**
 * 用户资源守卫 - 确保用户只能访问自己的用户信息
 */
const userResourceGuard = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { userId, userType } = ctx.req;
    
    // 管理员可以访问所有用户信息
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    const requestedUserId = args.userId || args.id;
    
    // 如果没有指定用户ID，可能是访问自己的信息
    if (!requestedUserId) {
      return true;
    }
    
    // 用户只能访问自己的信息
    if (requestedUserId !== userId) {
      logger.warn(`用户尝试访问其他用户信息: ${info.fieldName}, 请求用户: ${requestedUserId}, 当前用户: ${userId}`);
      return new Error('只能访问自己的用户信息');
    }
    
    return true;
  }
);

/**
 * 菜单资源守卫 - 确保餐厅只能管理自己的菜单
 */
const menuResourceGuard = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { restaurantId, userType } = ctx.req;
    
    // 管理员可以管理所有菜单
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    // 非餐厅用户不能管理菜单
    if (userType !== USER_ROLES.RESTAURANT) {
      logger.warn(`非餐厅用户尝试管理菜单: ${info.fieldName}, 用户类型: ${userType}`);
      return new Error('无权管理菜单');
    }
    
    // 检查菜单相关的餐厅ID
    const requestedRestaurantId = args.restaurant || args.restaurantId;
    
    if (requestedRestaurantId && requestedRestaurantId !== restaurantId) {
      logger.warn(`餐厅用户尝试管理其他餐厅菜单: ${info.fieldName}, 请求餐厅: ${requestedRestaurantId}, 用户餐厅: ${restaurantId}`);
      return new Error('只能管理自己餐厅的菜单');
    }
    
    return true;
  }
);

/**
 * 仪表板资源守卫 - 确保餐厅只能查看自己的仪表板数据
 */
const dashboardResourceGuard = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { restaurantId, userType } = ctx.req;
    
    // 管理员可以查看所有仪表板数据
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    // 非餐厅用户不能查看仪表板
    if (userType !== USER_ROLES.RESTAURANT) {
      logger.warn(`非餐厅用户尝试查看仪表板: ${info.fieldName}, 用户类型: ${userType}`);
      return new Error('无权查看仪表板数据');
    }
    
    // 检查仪表板查询的餐厅ID
    const requestedRestaurantId = args.restaurant || args.restaurantId;
    
    if (requestedRestaurantId && requestedRestaurantId !== restaurantId) {
      logger.warn(`餐厅用户尝试查看其他餐厅仪表板: ${info.fieldName}, 请求餐厅: ${requestedRestaurantId}, 用户餐厅: ${restaurantId}`);
      return new Error('只能查看自己餐厅的仪表板数据');
    }
    
    return true;
  }
);

/**
 * 通用资源所有权检查器
 */
const createResourceOwnershipChecker = (resourceType) => {
  return rule({ cache: 'contextual' })(
    async (parent, args, ctx, info) => {
      const { restaurantId, userId, userType } = ctx.req;
      
      // 管理员可以访问所有资源
      if (userType === USER_ROLES.ADMIN) {
        return true;
      }
      
      logger.info(`资源所有权检查: ${resourceType}, 操作: ${info.fieldName}, 用户: ${userId}, 类型: ${userType}`);
      
      // 根据资源类型进行不同的检查
      switch (resourceType) {
        case 'restaurant':
          return restaurantResourceGuard.resolve(parent, args, ctx, info);
        case 'order':
          return orderResourceGuard.resolve(parent, args, ctx, info);
        case 'user':
          return userResourceGuard.resolve(parent, args, ctx, info);
        case 'menu':
          return menuResourceGuard.resolve(parent, args, ctx, info);
        case 'dashboard':
          return dashboardResourceGuard.resolve(parent, args, ctx, info);
        default:
          logger.warn(`未知资源类型: ${resourceType}`);
          return new Error('未知资源类型');
      }
    }
  );
};

module.exports = {
  restaurantResourceGuard,
  orderResourceGuard,
  userResourceGuard,
  menuResourceGuard,
  dashboardResourceGuard,
  createResourceOwnershipChecker
};
