/**
 * GraphQL权限规则定义
 * 定义各种权限检查规则
 */

const { rule, and, or, not } = require('graphql-shield');
const { USER_ROLES, AUTH_TYPES, WHATSAPP_TOKEN_ALLOWED_OPERATIONS } = require('./constants');
const logger = require('../../helpers/logger');

/**
 * 基础认证规则 - 检查是否为标准JWT认证用户
 */
const isAuthenticated = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const isAuth = ctx.req.isAuth === true;
    if (!isAuth) {
      logger.warn(`未认证用户尝试访问: ${info.fieldName}`);
    }
    return isAuth || new Error('用户需要登录');
  }
);

/**
 * X-WhatsAppW-Token认证规则 - 仅限制来自web页面的特定操作
 */
const isWebWhatsAppToken = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // 检查是否有X-WhatsAppW-Token认证
    const hasWhatsAppAuth = ctx.whatsAppAuth === true && !!ctx.whatsAppCustomerId;

    if (!hasWhatsAppAuth) {
      logger.warn(`无效的X-WhatsAppW-Token尝试访问: ${info.fieldName}`);
      return new Error('需要有效的X-WhatsAppW-Token认证');
    }

    // 检查当前操作是否在允许列表中
    if (!WHATSAPP_TOKEN_ALLOWED_OPERATIONS.includes(info.fieldName)) {
      logger.warn(`X-WhatsAppW-Token尝试访问未授权操作: ${info.fieldName}`);
      return new Error(`X-WhatsAppW-Token不允许访问操作: ${info.fieldName}`);
    }

    logger.debug(`X-WhatsAppW-Token认证成功: ${info.fieldName}, customerId: ${ctx.whatsAppCustomerId}`);
    return true;
  }
);

/**
 * 内部调用规则 - 允许通过customerId参数的内部调用
 */
const allowInternalCall = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // 如果有customerId参数，说明是内部调用，直接允许
    if (args.customerId) {
      logger.debug(`内部调用: ${info.fieldName}, customerId: ${args.customerId}`);
      return true;
    }

    // 否则拒绝
    return new Error('此操作仅允许内部调用');
  }
);

/**
 * 管理员权限规则
 */
const isAdmin = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const isAdminUser = ctx.req.userType === USER_ROLES.ADMIN;
    if (!isAdminUser) {
      logger.warn(`非管理员用户尝试访问管理员功能: ${info.fieldName}, 用户类型: ${ctx.req.userType}`);
    }
    return isAdminUser || new Error('需要管理员权限');
  }
);

/**
 * 餐厅用户权限规则
 */
const isRestaurantUser = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const userType = ctx.req.userType;
    const isRestaurantOrAdmin = userType === USER_ROLES.RESTAURANT || userType === USER_ROLES.ADMIN;
    if (!isRestaurantOrAdmin) {
      logger.warn(`非餐厅用户尝试访问餐厅功能: ${info.fieldName}, 用户类型: ${userType}`);
    }
    return isRestaurantOrAdmin || new Error('仅餐厅或管理员可访问');
  }
);

/**
 * 客户用户权限规则
 */
const isCustomerUser = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const userType = ctx.req.userType;
    const isCustomerOrAdmin = userType === USER_ROLES.CUSTOMER || userType === USER_ROLES.ADMIN;
    if (!isCustomerOrAdmin) {
      logger.warn(`非客户用户尝试访问客户功能: ${info.fieldName}, 用户类型: ${userType}`);
    }
    return isCustomerOrAdmin || new Error('仅客户或管理员可访问');
  }
);

/**
 * 资源所有权规则 - 检查用户是否有权访问特定资源
 */
const isResourceOwner = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { restaurantId, userId, userType } = ctx.req;
    
    // 管理员可以访问所有资源
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    // 餐厅用户只能访问自己的餐厅资源
    if (userType === USER_ROLES.RESTAURANT) {
      // 检查餐厅相关参数
      if (args.restaurant && args.restaurant !== restaurantId) {
        logger.warn(`餐厅用户尝试访问其他餐厅资源: ${info.fieldName}, 请求餐厅: ${args.restaurant}, 用户餐厅: ${restaurantId}`);
        return new Error('无权访问其他餐厅的资源');
      }
      if (args.restaurantId && args.restaurantId !== restaurantId) {
        logger.warn(`餐厅用户尝试访问其他餐厅资源: ${info.fieldName}, 请求餐厅: ${args.restaurantId}, 用户餐厅: ${restaurantId}`);
        return new Error('无权访问其他餐厅的资源');
      }
    }
    
    // 客户用户只能访问自己的资源
    if (userType === USER_ROLES.CUSTOMER) {
      if (args.userId && args.userId !== userId) {
        logger.warn(`客户用户尝试访问其他用户资源: ${info.fieldName}, 请求用户: ${args.userId}, 当前用户: ${userId}`);
        return new Error('无权访问其他用户的资源');
      }
    }
    
    return true;
  }
);

/**
 * 订单所有权规则 - 检查用户是否有权访问特定订单
 */
const isOrderOwner = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { restaurantId, userId, userType } = ctx.req;
    
    // 管理员可以访问所有订单
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    // 如果有订单ID参数，需要检查订单所有权
    if (args.orderId || args.id) {
      // 这里需要查询数据库验证订单所有权
      // 为了简化，暂时允许通过，实际应用中需要查询订单表
      logger.info(`订单所有权检查: ${info.fieldName}, 订单: ${args.orderId || args.id}, 用户: ${userId}, 类型: ${userType}`);
    }
    
    return true;
  }
);

/**
 * 敏感配置访问规则 - 仅管理员可访问敏感配置
 */
const isSensitiveConfigurationAccess = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const isAdminUser = ctx.req.userType === USER_ROLES.ADMIN;
    if (!isAdminUser) {
      logger.error(`非管理员用户尝试访问敏感配置: ${info.fieldName}, 用户类型: ${ctx.req.userType}, 用户ID: ${ctx.req.userId}`);
    }
    return isAdminUser || new Error('访问敏感配置需要管理员权限');
  }
);

/**
 * 用户管理权限规则 - 仅管理员可管理用户
 */
const isUserManager = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const isAdminUser = ctx.req.userType === USER_ROLES.ADMIN;
    if (!isAdminUser) {
      logger.error(`非管理员用户尝试管理用户: ${info.fieldName}, 用户类型: ${ctx.req.userType}, 用户ID: ${ctx.req.userId}`);
    }
    return isAdminUser || new Error('用户管理需要管理员权限');
  }
);

/**
 * 自我管理规则 - 用户可以管理自己的信息
 */
const isSelfManagement = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { userId, userType } = ctx.req;
    
    // 管理员可以管理所有用户
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    // 用户只能管理自己
    if (args.userId && args.userId !== userId) {
      logger.warn(`用户尝试管理其他用户信息: ${info.fieldName}, 请求用户: ${args.userId}, 当前用户: ${userId}`);
      return new Error('只能管理自己的信息');
    }
    
    return true;
  }
);

module.exports = {
  isAuthenticated,
  isWebWhatsAppToken,
  allowInternalCall,
  isAdmin,
  isRestaurantUser,
  isCustomerUser,
  isResourceOwner,
  isOrderOwner,
  isSensitiveConfigurationAccess,
  isUserManager,
  isSelfManagement
};
